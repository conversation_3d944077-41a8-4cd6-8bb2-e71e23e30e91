/**
 * Bedalator Popup Script
 * Handles the popup interface and communication with content script
 */

class BedalatorPopup {
  constructor() {
    this.currentData = null;
    this.updateInterval = null;
    this.autoUpdateCount = 0;
    this.init();
  }

  init() {
    this.setupEventListeners();
    // Automatically load data when popup opens
    this.validateAndLoadData();
    this.startAutoRefresh();
    console.log('Bedalator Popup: Initialized with automatic data loading');
  }

  /**
   * Validate tracking tabs and load data appropriately
   */
  async validateAndLoadData() {
    // Check if any tracking tabs are available
    const hasTrackingTabs = await new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'CHECK_TRACKING_TABS_AVAILABLE' }, (response) => {
        if (chrome.runtime.lastError) {
          resolve(false);
        } else {
          resolve(response || false);
        }
      });
    });

    if (!hasTrackingTabs) {
      // No tracking tabs available, show appropriate error
      this.showError('Bitte öffnen Sie die Bedatime-Seite und lassen Sie sie geöffnet.');
      return;
    }

    // Tracking tabs available, proceed with normal data loading
    this.loadWorkData(true); // Force fresh data on popup open
  }

  /**
   * Set up event listeners for UI interactions
   */
  setupEventListeners() {
    // No manual controls needed - extension auto-refreshes
  }

  /**
   * Load work data from content script or storage
   */
  async loadWorkData(forceRefresh = false, isAutoUpdate = false) {
    try {
      // Show different indicators for manual vs auto updates
      if (isAutoUpdate) {
        this.showAutoUpdateIndicator(true);
      } else {
        this.showLoading(true);
      }
      this.hideError();

      let workData = null;

      // Always try to get fresh data from content script first
      workData = await this.getDataFromContentScript();

      // If no fresh data and not forcing refresh, try stored data
      if (!workData && !forceRefresh) {
        workData = await this.getStoredData();
      }

      if (workData) {
        this.currentData = workData;
        this.updateUI(workData);
      } else {
        // Only show errors for manual refreshes, not auto-updates
        if (!isAutoUpdate) {
          // Check if we're on the right page
          const isOnTrackingPage = await this.checkIfOnTrackingPage();
          if (isOnTrackingPage) {
            this.showError('Zeiterfassungselemente gefunden, aber keine Daten verfügbar. Bitte versuchen Sie es erneut.');
          } else {
            this.showError('Bitte öffnen Sie die Bedatime-Seite und lassen Sie sie geöffnet.');
          }
        }
      }
    } catch (error) {
      console.error('Bedalator Popup: Error loading work data:', error);
      // Only show errors for manual refreshes, not auto-updates
      if (!isAutoUpdate) {
        this.showError('Fehler beim Laden der Arbeitsdaten. Bitte versuchen Sie es erneut.');
      }
    } finally {
      if (isAutoUpdate) {
        this.showAutoUpdateIndicator(false);
      } else {
        this.showLoading(false);
      }
    }
  }

  /**
   * Get data from content script or cross-tab
   */
  async getDataFromContentScript() {
    // First try the active tab
    const activeTabData = await this.getDataFromActiveTab();
    if (activeTabData) {
      return activeTabData;
    }

    // If no data from active tab, try cross-tab data
    return await this.getCrossTabData();
  }

  /**
   * Get data from the currently active tab
   */
  async getDataFromActiveTab() {
    return new Promise((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          // Set a timeout to avoid hanging
          const timeout = setTimeout(() => {
            console.log('Bedalator Popup: Active tab timeout');
            resolve(null);
          }, 2000); // Shorter timeout for active tab

          chrome.tabs.sendMessage(tabs[0].id, { type: 'GET_WORK_DATA' }, (response) => {
            clearTimeout(timeout);
            if (chrome.runtime.lastError) {
              console.log('Bedalator Popup: Active tab not available:', chrome.runtime.lastError.message);
              resolve(null);
            } else {
              resolve(response);
            }
          });
        } else {
          resolve(null);
        }
      });
    });
  }

  /**
   * Get data from any tracking tab via background script
   */
  async getCrossTabData() {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'GET_CROSS_TAB_DATA' }, (response) => {
        if (chrome.runtime.lastError) {
          console.log('Bedalator Popup: Cross-tab data not available:', chrome.runtime.lastError.message);
          resolve(null);
        } else {
          resolve(response);
        }
      });
    });
  }

  /**
   * Check if any tracking tabs are available
   */
  async checkIfOnTrackingPage() {
    // First check if background script has any tracking tabs
    const hasTrackingTabs = await new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'CHECK_TRACKING_TABS_AVAILABLE' }, (response) => {
        if (chrome.runtime.lastError) {
          resolve(false);
        } else {
          resolve(response || false);
        }
      });
    });

    if (hasTrackingTabs) {
      return true;
    }

    // Also check active tab for immediate compatibility
    const activeTabCompatible = await new Promise((resolve) => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, { type: 'CHECK_PAGE_COMPATIBILITY' }, (response) => {
            if (chrome.runtime.lastError) {
              resolve(false);
            } else {
              resolve(response || false);
            }
          });
        } else {
          resolve(false);
        }
      });
    });

    return activeTabCompatible;
  }

  /**
   * Get current data from storage (only if tracking tabs are available)
   */
  async getStoredData() {
    return new Promise((resolve) => {
      // First check if any tracking tabs are available
      chrome.runtime.sendMessage({ type: 'CHECK_TRACKING_TABS_AVAILABLE' }, (hasTrackingTabs) => {
        if (chrome.runtime.lastError || !hasTrackingTabs) {
          console.log('Bedalator Popup: No tracking tabs available, not using stored data');
          resolve(null);
          return;
        }

        // Only get stored data if tracking tabs are available
        chrome.storage.local.get(['currentWorkData'], (result) => {
          if (chrome.runtime.lastError) {
            console.error('Bedalator Popup: Error getting stored data:', chrome.runtime.lastError);
            resolve(null);
          } else {
            resolve(result.currentWorkData || null);
          }
        });
      });
    });
  }

  /**
   * Update the UI with work data
   */
  updateUI(data) {
    // Update status indicator
    this.updateStatusIndicator(data.workStatus);

    // Update time displays
    const totalTimeElement = document.getElementById('totalTime');
    if (totalTimeElement) {
      totalTimeElement.textContent = data.totalTime || '00:00';
    }

    // Show/hide current session
    const currentSessionContainer = document.getElementById('currentSessionContainer');
    const currentSessionTimeElement = document.getElementById('currentSessionTime');

    if (currentSessionContainer) {
      if (data.workStatus === 'working' && data.currentSessionTime) {
        currentSessionContainer.style.display = 'block';
        if (currentSessionTimeElement) {
          currentSessionTimeElement.textContent = data.currentSessionTime;
        }
      } else {
        currentSessionContainer.style.display = 'none';
      }
    }

    // Update sessions list
    this.updateSessionsList(data.timeData || []);

    // Update last update time
    this.updateLastUpdateTime(data.timestamp);
  }

  /**
   * Update status indicator
   */
  updateStatusIndicator(status) {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');

    if (statusDot && statusText) {
      switch (status) {
        case 'working':
          statusDot.className = 'status-dot working';
          statusText.textContent = 'Aktiv';
          break;
        case 'not-working':
          statusDot.className = 'status-dot not-working';
          statusText.textContent = 'Inaktiv';
          break;
        default:
          statusDot.className = 'status-dot unknown';
          statusText.textContent = 'Unbekannt';
      }
    } else {
      console.warn('Bedalator Popup: Status indicator elements not found');
    }
  }

  /**
   * Update sessions list
   */
  updateSessionsList(sessions) {
    const sessionsList = document.getElementById('sessionsList');
    const noSessions = document.getElementById('noSessions');

    if (!sessionsList) {
      console.warn('Bedalator Popup: Sessions list element not found');
      return;
    }

    if (!sessions || sessions.length === 0) {
      if (noSessions) {
        noSessions.style.display = 'block';
      }
      sessionsList.innerHTML = '<div class="no-sessions">Keine Sitzungen erkannt</div>';
      return;
    }

    if (noSessions) {
      noSessions.style.display = 'none';
    }
    
    const sessionsHTML = sessions.map((session, index) => {
      const isActive = session.start && !session.end;
      const endTime = session.end || (isActive ? 'Aktiv' : 'Unbekannt');
      const duration = this.calculateSessionDuration(session);
      
      return `
        <div class="session-item ${isActive ? 'active' : ''}">
          <div class="session-times">
            <span class="start-time">${session.start}</span>
            <span class="separator">→</span>
            <span class="end-time ${isActive ? 'active' : ''}">${endTime}</span>
          </div>
          <div class="session-duration">${duration}</div>
        </div>
      `;
    }).join('');

    sessionsList.innerHTML = sessionsHTML;
  }

  /**
   * Calculate session duration
   */
  calculateSessionDuration(session) {
    if (!session.start) return '00:00';

    const startMinutes = this.timeToMinutes(session.start);
    let endMinutes;

    if (session.end) {
      endMinutes = this.timeToMinutes(session.end);
    } else {
      // Active session - use current time
      const now = new Date();
      endMinutes = now.getHours() * 60 + now.getMinutes();
    }

    let duration = endMinutes - startMinutes;
    if (duration < 0) {
      // Handle sessions spanning midnight
      duration = (24 * 60 - startMinutes) + endMinutes;
    }

    return this.minutesToTime(duration);
  }

  /**
   * Convert time string to minutes
   */
  timeToMinutes(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Convert minutes to time string
   */
  minutesToTime(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  /**
   * Update last update time
   */
  updateLastUpdateTime(timestamp) {
    const lastUpdateElement = document.getElementById('lastUpdate');
    if (lastUpdateElement) {
      if (timestamp) {
        const date = new Date(timestamp);
        lastUpdateElement.textContent = date.toLocaleTimeString('de-DE');
      } else {
        lastUpdateElement.textContent = 'Nie';
      }
    } else {
      console.warn('Bedalator Popup: Last update element not found');
    }
  }

  /**
   * Show/hide loading indicator
   */
  showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
      loadingIndicator.style.display = show ? 'flex' : 'none';
    } else {
      console.warn('Bedalator Popup: Loading indicator element not found');
    }
  }

  /**
   * Show error message
   */
  showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    if (errorMessage && errorText) {
      errorText.textContent = message;
      errorMessage.style.display = 'block';
    } else {
      console.warn('Bedalator Popup: Error message elements not found');
      console.error('Error to display:', message);
    }
  }

  /**
   * Hide error message
   */
  hideError() {
    const errorMessage = document.getElementById('errorMessage');
    if (errorMessage) {
      errorMessage.style.display = 'none';
    } else {
      console.warn('Bedalator Popup: Error message element not found');
    }
  }

  /**
   * Show/hide auto-update indicator
   */
  showAutoUpdateIndicator(show) {
    const indicator = document.getElementById('autoUpdateIndicator');
    if (indicator) {
      indicator.style.display = show ? 'inline' : 'none';
      if (show) {
        console.log('🔄 Bedalator: Auto-update in progress...');
      } else {
        console.log('✅ Bedalator: Auto-update completed');
      }
    }
  }



  /**
   * Start auto-refresh for real-time updates
   */
  startAutoRefresh() {
    // Clear any existing interval
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    // Start with a shorter interval for immediate responsiveness
    this.updateInterval = setInterval(() => {
      // Only auto-refresh if we have data and no errors are showing
      const errorMessage = document.getElementById('errorMessage');
      const isErrorVisible = errorMessage && errorMessage.style.display !== 'none';

      if (!isErrorVisible) {
        // Check if tracking tabs are still available before auto-updating
        chrome.runtime.sendMessage({ type: 'CHECK_TRACKING_TABS_AVAILABLE' }, (hasTrackingTabs) => {
          if (chrome.runtime.lastError || !hasTrackingTabs) {
            // No tracking tabs available, show error and stop auto-updates
            this.showError('Bitte öffnen Sie die Bedatime-Seite und lassen Sie sie geöffnet.');
            this.currentData = null;
            return;
          }

          // Tracking tabs available, proceed with auto-update
          this.autoUpdateCount++;
          console.log(`🔄 Bedalator Auto-update #${this.autoUpdateCount} starting...`);

          if (this.currentData) {
            if (this.currentData.workStatus === 'working') {
              // More frequent updates when actively working (every 30 seconds)
              this.loadWorkData(false, true); // true = isAutoUpdate
            } else {
              // Less frequent updates when not working (every 60 seconds)
              // But still check periodically
              this.loadWorkData(false, true); // true = isAutoUpdate
            }
          } else {
            // Try to load data if we don't have any (every 30 seconds)
            this.loadWorkData(false, true); // true = isAutoUpdate
          }
        });
      }
    }, 30000); // 30 seconds for good balance between responsiveness and performance

    console.log('Bedalator Popup: Auto-refresh started (30-second intervals)');
  }

  /**
   * Stop auto-refresh
   */
  stopAutoRefresh() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const popup = new BedalatorPopup();
  
  // Cleanup when popup is closed
  window.addEventListener('beforeunload', () => {
    popup.stopAutoRefresh();
  });
});
